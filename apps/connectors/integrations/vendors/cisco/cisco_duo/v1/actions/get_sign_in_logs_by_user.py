from apps.connectors.integrations.actions.user.get_sign_in_logs import (
    GetSignInLogsByUser,
    SignInLogsByUserIdArgs,
    SignInLogsResult,
)
from apps.connectors.integrations.schemas.tap_result import ErrorDetail
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.api import CiscoDuoV1Api
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllHosts,  # We'll create a proper user management health check later
)

from .authentication import normalize_authentication_log


class CiscoDuoV1GetSignInLogsByUser(GetSignInLogsByUser):
    """
    Get sign-in logs for a specific user from Cisco Duo.

    This action uses the Duo Admin API to fetch authentication logs for a user
    and normalizes them to OCSF Authentication format.
    Reference: https://duo.com/docs/adminapi#authentication-logs
    """

    def execute(self, args: SignInLogsByUserIdArgs) -> SignInLogsResult:
        """
        Execute the get sign-in logs by user action.

        Args:
            args: SignInLogsByUserIdArgs containing user_id and time range

        Returns:
            SignInLogsResult: List of normalized authentication logs
        """
        api: CiscoDuoV1Api = self.integration.get_api()

        # Convert datetime to Unix timestamp if provided
        mintime = int(args.start_time.timestamp()) if args.start_time else None
        maxtime = int(args.end_time.timestamp()) if args.end_time else None

        # Call the Duo API to get authentication logs
        response = api.get_authentication_logs(
            users=args.user_id.value,
            mintime=mintime,
            maxtime=maxtime
        )

        # Check if the operation was successful
        if response.get("stat") == "OK":
            logs = response.get("response", [])

            # Normalize each log to OCSF format
            normalized_logs = [normalize_authentication_log(log) for log in logs]

            return SignInLogsResult(result=normalized_logs)
        else:
            error_message = response.get("message", "Failed to retrieve authentication logs")
            return SignInLogsResult(
                error=ErrorDetail(message=f"Failed to retrieve logs: {error_message}")
            )

    def get_permission_checks(self): # pragma: no cover
        return [ReadAllHosts]
